<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Commons Visualization - Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border-left: 4px solid #667eea;
            background: #f8f9fa;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #5a67d8;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background: #e6f3ff;
            border: 1px solid #b3d9ff;
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Digital Commons Visualization - Test Suite</h1>
        <p>This page tests the core functionality of the digital commons visualization.</p>

        <div class="test-section">
            <h3>1. Data Loading Test</h3>
            <p>Test if the JSON data file loads correctly.</p>
            <button class="test-button" onclick="testDataLoading()">Test Data Loading</button>
            <div id="data-test-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. Data Processor Test</h3>
            <p>Test the data processing functionality.</p>
            <button class="test-button" onclick="testDataProcessor()">Test Data Processor</button>
            <div id="processor-test-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. Visualization Components Test</h3>
            <p>Test if D3.js and visualization components work.</p>
            <button class="test-button" onclick="testVisualization()">Test Visualization</button>
            <div id="viz-test-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. Performance Calculation Test</h3>
            <p>Test performance score calculations with sample data.</p>
            <button class="test-button" onclick="testPerformanceCalculation()">Test Performance</button>
            <div id="performance-test-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>5. Launch Full Visualization</h3>
            <p>Open the complete visualization in a new tab.</p>
            <button class="test-button" onclick="launchVisualization()">Launch Visualization</button>
        </div>
    </div>

    <!-- Include the necessary scripts -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="./src/js/data.js"></script>

    <script>
        let testData = null;
        let dataProcessor = null;

        async function testDataLoading() {
            const resultDiv = document.getElementById('data-test-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Loading data...';

            try {
                const response = await fetch('./src/data/models.json');
                testData = await response.json();
                
                if (testData && testData.researchModels && testData.scenarios) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        <strong>✓ Data loaded successfully!</strong><br>
                        - Found ${Object.keys(testData.researchModels).length} research models<br>
                        - Found ${testData.scenarios.length} scenarios<br>
                        - Performance model has ${testData.researchModels.performanceModel.factors.length} factors
                    `;
                } else {
                    throw new Error('Invalid data structure');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `<strong>✗ Error loading data:</strong> ${error.message}`;
            }
        }

        async function testDataProcessor() {
            const resultDiv = document.getElementById('processor-test-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing data processor...';

            try {
                if (!testData) {
                    await testDataLoading();
                }

                dataProcessor = new DataProcessor();
                dataProcessor.setData(testData);

                // Test with sample parameters
                const sampleParams = {
                    informationOpenness: 0.8,
                    communityAutonomy: 0.7,
                    resourceAccessibility: 0.6,
                    governanceTransparency: 0.9,
                    externalSupport: 0.5,
                    centralizationPressure: 0.3
                };

                const performanceScore = dataProcessor.calculatePerformanceScore(sampleParams);
                const pathway = dataProcessor.getMostLikelyPathway(sampleParams);
                const failureRisk = dataProcessor.calculateFailureRisk(sampleParams);

                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `
                    <strong>✓ Data processor working correctly!</strong><br>
                    - Performance Score: ${(performanceScore * 100).toFixed(1)}%<br>
                    - Most Likely Pathway: ${pathway.pathway ? pathway.pathway.name : 'None'}<br>
                    - Failure Risk: ${(failureRisk.totalRisk * 100).toFixed(1)}%
                `;
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `<strong>✗ Error in data processor:</strong> ${error.message}`;
            }
        }

        function testVisualization() {
            const resultDiv = document.getElementById('viz-test-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing visualization components...';

            try {
                // Test D3.js availability
                if (typeof d3 === 'undefined') {
                    throw new Error('D3.js not loaded');
                }

                // Test basic D3 functionality
                const testSvg = d3.select('body').append('svg')
                    .attr('width', 100)
                    .attr('height', 100)
                    .style('display', 'none');

                testSvg.append('circle')
                    .attr('cx', 50)
                    .attr('cy', 50)
                    .attr('r', 20)
                    .attr('fill', 'blue');

                // Clean up test element
                testSvg.remove();

                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `
                    <strong>✓ Visualization components ready!</strong><br>
                    - D3.js version: ${d3.version}<br>
                    - SVG creation: Working<br>
                    - Basic shapes: Working
                `;
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `<strong>✗ Visualization error:</strong> ${error.message}`;
            }
        }

        async function testPerformanceCalculation() {
            const resultDiv = document.getElementById('performance-test-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing performance calculations...';

            try {
                if (!dataProcessor) {
                    await testDataProcessor();
                }

                // Test different scenarios
                const scenarios = [
                    {
                        name: 'High Performance',
                        params: { informationOpenness: 0.9, communityAutonomy: 0.8, resourceAccessibility: 0.9, governanceTransparency: 0.9, externalSupport: 0.7, centralizationPressure: 0.1 }
                    },
                    {
                        name: 'Low Performance',
                        params: { informationOpenness: 0.2, communityAutonomy: 0.3, resourceAccessibility: 0.4, governanceTransparency: 0.3, externalSupport: 0.2, centralizationPressure: 0.9 }
                    }
                ];

                let results = '';
                scenarios.forEach(scenario => {
                    const score = dataProcessor.calculatePerformanceScore(scenario.params);
                    results += `- ${scenario.name}: ${(score * 100).toFixed(1)}%<br>`;
                });

                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `
                    <strong>✓ Performance calculations working!</strong><br>
                    ${results}
                    <small>Results show expected variation between scenarios.</small>
                `;
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `<strong>✗ Performance calculation error:</strong> ${error.message}`;
            }
        }

        function launchVisualization() {
            window.open('./index.html', '_blank');
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testDataLoading();
            }, 500);
        });
    </script>
</body>
</html>
