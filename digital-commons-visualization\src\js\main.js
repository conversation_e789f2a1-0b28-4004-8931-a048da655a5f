// Main application entry point
// Digital Commons Performance Visualization

class DigitalCommonsApp {
    constructor() {
        this.visualization = null;
        this.isLoaded = false;
    }

    async init() {
        console.log('Digital Commons Visualization Loading...');

        try {
            // Initialize the visualization
            this.visualization = new DigitalCommonsVisualization('#visualization-container');

            // Load data
            await this.visualization.loadData();

            // Initialize the visualization components
            this.visualization.init();

            // Set up event listeners
            this.setupEventListeners();

            this.isLoaded = true;
            console.log('Digital Commons Visualization Loaded Successfully!');

            // Show welcome message
            this.showWelcomeMessage();

        } catch (error) {
            console.error('Error initializing visualization:', error);
            this.showErrorMessage(error);
        }
    }

    setupEventListeners() {
        // Scenario change handler
        document.addEventListener('change', (event) => {
            if (event.target.id === 'scenario-select') {
                this.handleScenarioChange(event.target.value);
            }
        });

        // Parameter slider handlers
        document.addEventListener('input', (event) => {
            if (event.target.type === 'range' && event.target.id.startsWith('slider-')) {
                this.handleParameterChange(event.target);
            }
        });

        // Window resize handler
        window.addEventListener('resize', () => {
            if (this.isLoaded) {
                this.handleResize();
            }
        });
    }

    handleScenarioChange(scenarioName) {
        console.log(`Scenario changed to: ${scenarioName}`);
        this.visualization.currentScenario = scenarioName;
        this.visualization.updateVisualization();
        this.updateScenarioDescription(scenarioName);
    }

    handleParameterChange(slider) {
        const parameter = slider.id.replace('slider-', '');
        const value = parseFloat(slider.value);
        console.log(`Parameter ${parameter} changed to: ${value}`);

        // Update the visualization in real-time
        this.visualization.updateVisualization();

        // Update the parameter display
        this.updateParameterDisplay(parameter, value);
    }

    handleResize() {
        // Debounce resize events
        clearTimeout(this.resizeTimeout);
        this.resizeTimeout = setTimeout(() => {
            console.log('Handling window resize...');
            // Re-render visualizations with new dimensions
            this.visualization.init();
        }, 250);
    }

    updateScenarioDescription(scenarioName) {
        const descriptions = {
            'Open Democratic System': 'A political system with high information openness, strong community autonomy, and transparent governance structures.',
            'Moderate Centralization': 'A mixed system with moderate levels of centralization, balanced between control and openness.',
            'High Centralization': 'A highly centralized system with significant information control and limited community autonomy.'
        };

        const descriptionElement = document.getElementById('scenario-description');
        if (descriptionElement) {
            descriptionElement.textContent = descriptions[scenarioName] || 'Custom scenario configuration.';
        }
    }

    updateParameterDisplay(parameter, value) {
        const displayElement = document.getElementById(`${parameter}-value`);
        if (displayElement) {
            displayElement.textContent = (value * 100).toFixed(0) + '%';
        }
    }

    showWelcomeMessage() {
        const welcomeDiv = document.createElement('div');
        welcomeDiv.className = 'welcome-message';
        welcomeDiv.innerHTML = `
            <div class="welcome-content">
                <h3>Welcome to the Digital Commons Performance Visualization</h3>
                <p>This interactive tool explores how digital commons develop in different political systems.</p>
                <p>Use the controls above to explore different scenarios and see how various factors affect digital commons performance.</p>
                <button onclick="this.parentElement.parentElement.style.display='none'">Get Started</button>
            </div>
        `;

        document.body.appendChild(welcomeDiv);

        // Auto-hide after 10 seconds
        setTimeout(() => {
            if (welcomeDiv.parentElement) {
                welcomeDiv.style.display = 'none';
            }
        }, 10000);
    }

    showErrorMessage(error) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `
            <div class="error-content">
                <h3>Error Loading Visualization</h3>
                <p>There was an error loading the digital commons visualization:</p>
                <pre>${error.message}</pre>
                <p>Please refresh the page and try again.</p>
            </div>
        `;

        document.body.appendChild(errorDiv);
    }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const app = new DigitalCommonsApp();
    app.init();
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DigitalCommonsApp;
}
