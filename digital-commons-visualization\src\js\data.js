// Data processing utilities for Digital Commons Visualization

class DataProcessor {
    constructor() {
        this.models = null;
        this.scenarios = null;
    }

    setData(data) {
        this.models = data.researchModels;
        this.scenarios = data.scenarios;
    }

    // Calculate overall performance score based on current parameters
    calculatePerformanceScore(parameters) {
        if (!this.models || !this.models.performanceModel) {
            return 0;
        }

        const factors = this.models.performanceModel.factors;
        let totalScore = 0;
        let totalWeight = 0;

        factors.forEach(factor => {
            const paramKey = this.getParameterKey(factor.name);
            let value = parameters[paramKey] || 0.5;
            
            // Apply impact direction (negative factors reduce score)
            if (factor.impact === 'negative') {
                value = 1 - value;
            }
            
            totalScore += value * factor.weight;
            totalWeight += factor.weight;
        });

        return totalWeight > 0 ? totalScore / totalWeight : 0;
    }

    // Determine most likely development pathway based on parameters
    getMostLikelyPathway(parameters) {
        if (!this.models || !this.models.developmentPathways) {
            return null;
        }

        const pathways = this.models.developmentPathways.pathways;
        let bestPathway = null;
        let bestScore = -1;

        pathways.forEach(pathway => {
            const score = this.calculatePathwayScore(pathway, parameters);
            if (score > bestScore) {
                bestScore = score;
                bestPathway = pathway;
            }
        });

        return {
            pathway: bestPathway,
            score: bestScore,
            probability: bestPathway ? bestPathway.probability * bestScore : 0
        };
    }

    // Calculate how well parameters match pathway conditions
    calculatePathwayScore(pathway, parameters) {
        const conditions = pathway.conditions;
        let totalScore = 0;
        let conditionCount = 0;

        Object.keys(conditions).forEach(paramKey => {
            const paramValue = parameters[paramKey];
            const [min, max] = conditions[paramKey];
            
            if (paramValue !== undefined) {
                // Calculate how well the parameter fits within the condition range
                let score = 0;
                if (paramValue >= min && paramValue <= max) {
                    // Perfect fit
                    score = 1;
                } else if (paramValue < min) {
                    // Below range - score decreases with distance
                    score = Math.max(0, 1 - (min - paramValue) / min);
                } else {
                    // Above range - score decreases with distance
                    score = Math.max(0, 1 - (paramValue - max) / (1 - max));
                }
                
                totalScore += score;
                conditionCount++;
            }
        });

        return conditionCount > 0 ? totalScore / conditionCount : 0;
    }

    // Calculate failure risk based on current parameters
    calculateFailureRisk(parameters) {
        if (!this.models || !this.models.failureModes) {
            return { totalRisk: 0, riskBreakdown: [] };
        }

        const modes = this.models.failureModes.modes;
        const riskBreakdown = [];
        let totalRisk = 0;

        modes.forEach(mode => {
            const risk = this.calculateModeRisk(mode, parameters);
            riskBreakdown.push({
                name: mode.name,
                risk: risk,
                frequency: mode.frequency,
                severity: mode.severity
            });
            totalRisk += risk * mode.frequency;
        });

        return {
            totalRisk: Math.min(1, totalRisk),
            riskBreakdown: riskBreakdown.sort((a, b) => b.risk - a.risk)
        };
    }

    // Calculate risk for a specific failure mode
    calculateModeRisk(mode, parameters) {
        // Risk calculation based on parameter values
        // Higher centralization and lower openness increase risk
        const centralization = parameters.centralizationPressure || 0;
        const openness = parameters.informationOpenness || 1;
        const autonomy = parameters.communityAutonomy || 1;
        const resources = parameters.resourceAccessibility || 1;

        let baseRisk = 0;

        switch (mode.name) {
            case 'Information Restrictions':
                baseRisk = centralization * 0.8 + (1 - openness) * 0.6;
                break;
            case 'Resource Constraints':
                baseRisk = (1 - resources) * 0.7 + centralization * 0.3;
                break;
            case 'Community Fragmentation':
                baseRisk = (1 - autonomy) * 0.6 + centralization * 0.4;
                break;
            case 'External Interference':
                baseRisk = centralization * 0.9 + (1 - autonomy) * 0.3;
                break;
            case 'Governance Conflicts':
                baseRisk = (1 - parameters.governanceTransparency || 0) * 0.5 + (1 - autonomy) * 0.3;
                break;
            default:
                baseRisk = centralization * 0.5;
        }

        return Math.min(1, baseRisk / 2); // Normalize to 0-1 range
    }

    // Generate recommendations based on current parameters
    generateRecommendations(parameters) {
        const performanceScore = this.calculatePerformanceScore(parameters);
        const pathway = this.getMostLikelyPathway(parameters);
        const failureRisk = this.calculateFailureRisk(parameters);
        
        const recommendations = [];

        // Performance-based recommendations
        if (performanceScore < 0.6) {
            recommendations.push({
                type: 'performance',
                priority: 'high',
                message: 'Consider increasing information openness and community autonomy to improve overall performance.',
                actions: ['Reduce information restrictions', 'Increase community self-governance', 'Improve resource accessibility']
            });
        }

        // Pathway-based recommendations
        if (pathway.probability < 0.3) {
            recommendations.push({
                type: 'pathway',
                priority: 'medium',
                message: 'Current conditions may not favor successful digital commons development.',
                actions: ['Review governance structures', 'Increase transparency', 'Build community support']
            });
        }

        // Risk-based recommendations
        if (failureRisk.totalRisk > 0.7) {
            const topRisk = failureRisk.riskBreakdown[0];
            recommendations.push({
                type: 'risk',
                priority: 'high',
                message: `High risk of failure due to ${topRisk.name.toLowerCase()}.`,
                actions: this.getRiskMitigationActions(topRisk.name)
            });
        }

        return recommendations;
    }

    getRiskMitigationActions(riskName) {
        const mitigationMap = {
            'Information Restrictions': [
                'Implement decentralized information systems',
                'Develop alternative communication channels',
                'Build international partnerships'
            ],
            'Resource Constraints': [
                'Diversify funding sources',
                'Optimize resource utilization',
                'Develop community contribution mechanisms'
            ],
            'Community Fragmentation': [
                'Establish clear governance procedures',
                'Create conflict resolution mechanisms',
                'Foster inclusive participation'
            ],
            'External Interference': [
                'Build legal protections',
                'Develop distributed governance',
                'Create international support networks'
            ],
            'Governance Conflicts': [
                'Implement democratic decision-making',
                'Establish mediation processes',
                'Create transparent procedures'
            ]
        };

        return mitigationMap[riskName] || ['Review and adjust current strategies'];
    }

    // Utility function to map factor names to parameter keys
    getParameterKey(factorName) {
        const mapping = {
            'Information Openness': 'informationOpenness',
            'Community Autonomy': 'communityAutonomy',
            'Resource Accessibility': 'resourceAccessibility',
            'Governance Transparency': 'governanceTransparency',
            'External Support': 'externalSupport',
            'Centralization Pressure': 'centralizationPressure'
        };
        return mapping[factorName] || 'informationOpenness';
    }

    // Generate sample data for testing
    generateSampleScenario() {
        return {
            name: 'Custom Scenario',
            parameters: {
                informationOpenness: Math.random(),
                communityAutonomy: Math.random(),
                resourceAccessibility: Math.random(),
                governanceTransparency: Math.random(),
                externalSupport: Math.random(),
                centralizationPressure: Math.random()
            }
        };
    }

    // Export data for analysis
    exportAnalysis(parameters) {
        return {
            timestamp: new Date().toISOString(),
            parameters: parameters,
            analysis: {
                performanceScore: this.calculatePerformanceScore(parameters),
                mostLikelyPathway: this.getMostLikelyPathway(parameters),
                failureRisk: this.calculateFailureRisk(parameters),
                recommendations: this.generateRecommendations(parameters)
            }
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataProcessor;
}
