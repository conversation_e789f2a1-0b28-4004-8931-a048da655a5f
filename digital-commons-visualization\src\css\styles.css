/* Digital Commons Visualization Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    text-align: center;
    margin-bottom: 2rem;
}

h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

.visualization-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin-bottom: 2rem;
}

.controls {
    margin-bottom: 2rem;
    padding: 1rem;
    background: #f1f3f4;
    border-radius: 6px;
}

.model-section {
    margin-bottom: 3rem;
}

.model-title {
    font-size: 1.5rem;
    color: #4a5568;
    margin-bottom: 1rem;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 0.5rem;
}

.chart-container {
    min-height: 400px;
    position: relative;
}

.legend {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
}

.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.9rem;
    pointer-events: none;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.2s;
}

.research-context {
    background: #e6f3ff;
    border-left: 4px solid #0066cc;
    padding: 1.5rem;
    margin: 2rem 0;
    border-radius: 0 6px 6px 0;
}

.research-context h3 {
    color: #0066cc;
    margin-bottom: 1rem;
}

/* Interactive Elements */
.bubble {
    cursor: pointer;
    transition: all 0.3s ease;
}

.bubble:hover {
    transform: scale(1.1);
}

.bubble:hover circle {
    stroke: #333;
    stroke-width: 2;
}

/* Chart Animations */
.chart-container svg {
    overflow: visible;
}

.chart-container path {
    transition: all 0.5s ease;
}

.chart-container circle {
    transition: all 0.3s ease;
}

.chart-container line {
    transition: all 0.3s ease;
}

/* Radar Chart Styling */
.radar-grid {
    stroke: #e2e8f0;
    stroke-width: 1;
    fill: none;
}

.radar-axis {
    stroke: #cbd5e0;
    stroke-width: 1;
}

.radar-label {
    font-size: 12px;
    font-weight: 500;
    fill: #4a5568;
}

.radar-polygon {
    fill: rgba(102, 126, 234, 0.3);
    stroke: #667eea;
    stroke-width: 2;
    transition: all 0.5s ease;
}

/* Pathway Chart Styling */
.pathway-bar {
    transition: all 0.3s ease;
    cursor: pointer;
}

.pathway-bar:hover {
    opacity: 0.9;
    stroke: #333;
    stroke-width: 1;
}

.pathway-label {
    font-size: 14px;
    font-weight: 500;
    fill: #2d3748;
}

.pathway-value {
    font-size: 12px;
    fill: #4a5568;
}

/* Impact Chart Styling */
.impact-line {
    fill: none;
    stroke-width: 2;
    transition: all 0.3s ease;
}

.impact-line:hover {
    stroke-width: 3;
}

.axis {
    font-size: 12px;
}

.axis-label {
    font-size: 14px;
    font-weight: 500;
    fill: #2d3748;
}

/* Analysis Panel Styling */
.analysis-panel {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
}

.analysis-section {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    border-left: 4px solid #667eea;
}

.analysis-section h4 {
    margin: 0 0 1rem 0;
    color: #2d3748;
    font-size: 1.1rem;
}

.score-display, .risk-display {
    margin-bottom: 0.5rem;
}

.score-bar, .risk-bar {
    width: 100%;
    height: 20px;
    background: #e2e8f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.score-fill, .risk-fill {
    height: 100%;
    transition: width 0.5s ease;
    border-radius: 10px;
}

.score-text, .risk-text {
    font-weight: 600;
    font-size: 1.1rem;
}

.recommendation {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.recommendation.high {
    background: #fed7d7;
    border-left: 4px solid #f56565;
    color: #c53030;
}

.recommendation.medium {
    background: #feebc8;
    border-left: 4px solid #ed8936;
    color: #c05621;
}

.recommendation.success {
    background: #c6f6d5;
    border-left: 4px solid #48bb78;
    color: #2f855a;
}

/* Loading States */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-size: 1.2rem;
    color: #718096;
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #e2e8f0;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sliders-container {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .chart-container {
        min-height: 300px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    h1 {
        font-size: 2rem;
    }

    .visualization-container {
        padding: 1rem;
    }

    .sliders-container {
        grid-template-columns: 1fr;
    }

    .legend {
        flex-direction: column;
        gap: 0.5rem;
    }

    .chart-container {
        min-height: 250px;
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 1.5rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    .model-title {
        font-size: 1.2rem;
    }

    .research-context {
        padding: 1rem;
    }
}
