// Digital Commons Visualization Components
// Using D3.js for interactive data visualization

class DigitalCommonsVisualization {
    constructor(containerId) {
        this.container = d3.select(containerId);
        this.data = null;
        this.dataProcessor = new DataProcessor();
        this.currentScenario = 'Open Democratic System';
        this.currentParameters = {};
        this.dimensions = {
            width: 800,
            height: 600,
            margin: { top: 40, right: 40, bottom: 60, left: 60 }
        };
        this.colors = {
            primary: '#667eea',
            secondary: '#764ba2',
            success: '#48bb78',
            warning: '#ed8936',
            danger: '#f56565',
            info: '#4299e1'
        };
        this.tooltip = null;
    }

    async loadData() {
        try {
            const response = await fetch('./src/data/models.json');
            this.data = await response.json();
            this.dataProcessor.setData(this.data);

            // Initialize current parameters with first scenario
            const firstScenario = this.data.scenarios.find(s => s.name === this.currentScenario);
            if (firstScenario) {
                this.currentParameters = { ...firstScenario.parameters };
            }

            console.log('Data loaded successfully:', this.data);
        } catch (error) {
            console.error('Error loading data:', error);
            throw error;
        }
    }

    init() {
        this.createLayout();
        this.createTooltip();
        this.createControls();
        this.renderPerformanceModel();
        this.renderDevelopmentPathways();
        this.renderFailureModes();
        this.renderCentralizationImpact();
        this.renderAnalysisPanel();
    }

    createTooltip() {
        // Remove existing tooltip
        d3.select('.tooltip').remove();

        this.tooltip = d3.select('body')
            .append('div')
            .attr('class', 'tooltip')
            .style('opacity', 0);
    }

    createLayout() {
        this.container.html(''); // Clear existing content
        
        // Create main sections
        const sections = [
            { id: 'controls', title: 'Scenario Controls' },
            { id: 'analysis', title: 'Real-time Analysis' },
            { id: 'performance', title: 'Performance Model' },
            { id: 'pathways', title: 'Development Pathways' },
            { id: 'failures', title: 'Failure Modes' },
            { id: 'centralization', title: 'Centralization Impact' }
        ];

        sections.forEach(section => {
            const sectionDiv = this.container.append('div')
                .attr('class', 'model-section')
                .attr('id', section.id);
            
            sectionDiv.append('h2')
                .attr('class', 'model-title')
                .text(section.title);
            
            sectionDiv.append('div')
                .attr('class', 'chart-container')
                .attr('id', `${section.id}-chart`);
        });
    }

    createControls() {
        const controlsContainer = d3.select('#controls-chart');
        
        // Scenario selector
        const scenarioGroup = controlsContainer.append('div')
            .attr('class', 'control-group');

        scenarioGroup.append('label')
            .text('Select Scenario:');

        const scenarioSelect = scenarioGroup.append('select')
            .attr('id', 'scenario-select')
            .on('change', (event) => {
                this.currentScenario = event.target.value;
                this.loadScenarioParameters();
                this.updateVisualization();
            });

        scenarioSelect.selectAll('option')
            .data(this.data.scenarios)
            .enter()
            .append('option')
            .attr('value', d => d.name)
            .text(d => d.name);

        // Scenario description
        controlsContainer.append('div')
            .attr('class', 'scenario-description')
            .attr('id', 'scenario-description')
            .text('Select a scenario to see its description.');

        // Parameter sliders
        const currentScenarioData = this.data.scenarios.find(s => s.name === this.currentScenario);
        const parameters = Object.keys(currentScenarioData.parameters);

        const sliderContainer = controlsContainer.append('div')
            .attr('class', 'sliders-container');

        parameters.forEach(param => {
            const sliderGroup = sliderContainer.append('div')
                .attr('class', 'slider-group');
            
            sliderGroup.append('label')
                .text(this.formatParameterName(param));
            
            const slider = sliderGroup.append('input')
                .attr('type', 'range')
                .attr('min', 0)
                .attr('max', 1)
                .attr('step', 0.01)
                .attr('value', currentScenarioData.parameters[param])
                .attr('id', `slider-${param}`)
                .on('input', (event) => {
                    this.currentParameters[param] = parseFloat(event.target.value);
                    this.updateParameterDisplay(param, event.target.value);
                    this.updateVisualization();
                });

            // Add value display
            sliderGroup.append('span')
                .attr('class', 'parameter-value')
                .attr('id', `${param}-value`)
                .text(`${Math.round(currentScenarioData.parameters[param] * 100)}%`);
        });
    }

    renderPerformanceModel() {
        const container = d3.select('#performance-chart');
        const model = this.data.researchModels.performanceModel;
        
        // Create radar chart for performance factors
        this.createRadarChart(container, model.factors, 'performance');
    }

    renderDevelopmentPathways() {
        const container = d3.select('#pathways-chart');
        const pathways = this.data.researchModels.developmentPathways.pathways;
        
        // Create sankey diagram for pathways
        this.createPathwaysChart(container, pathways);
    }

    renderFailureModes() {
        const container = d3.select('#failures-chart');
        const modes = this.data.researchModels.failureModes.modes;
        
        // Create bubble chart for failure modes
        this.createBubbleChart(container, modes);
    }

    renderCentralizationImpact() {
        const container = d3.select('#centralization-chart');
        const dimensions = this.data.researchModels.centralizationImpact.dimensions;

        // Create line chart showing impact curves
        this.createImpactChart(container, dimensions);
    }

    renderAnalysisPanel() {
        const container = d3.select('#analysis-chart');

        // Create analysis display
        const analysisDiv = container.append('div')
            .attr('class', 'analysis-panel');

        // Performance score section
        const performanceSection = analysisDiv.append('div')
            .attr('class', 'analysis-section');

        performanceSection.append('h4')
            .text('Performance Score');

        performanceSection.append('div')
            .attr('class', 'score-display')
            .attr('id', 'performance-score')
            .text('Calculating...');

        // Most likely pathway section
        const pathwaySection = analysisDiv.append('div')
            .attr('class', 'analysis-section');

        pathwaySection.append('h4')
            .text('Most Likely Development Pathway');

        pathwaySection.append('div')
            .attr('id', 'likely-pathway')
            .text('Analyzing...');

        // Failure risk section
        const riskSection = analysisDiv.append('div')
            .attr('class', 'analysis-section');

        riskSection.append('h4')
            .text('Failure Risk Assessment');

        riskSection.append('div')
            .attr('id', 'failure-risk')
            .text('Calculating...');

        // Recommendations section
        const recommendationsSection = analysisDiv.append('div')
            .attr('class', 'analysis-section');

        recommendationsSection.append('h4')
            .text('Recommendations');

        recommendationsSection.append('div')
            .attr('id', 'recommendations')
            .text('Generating...');
    }

    createRadarChart(container, factors, chartId) {
        const width = this.dimensions.width;
        const height = this.dimensions.height;
        const radius = Math.min(width, height) / 2 - 100;

        const svg = container.append('svg')
            .attr('width', width)
            .attr('height', height);

        const g = svg.append('g')
            .attr('transform', `translate(${width/2}, ${height/2})`);

        // Create scales
        const angleScale = d3.scaleLinear()
            .domain([0, factors.length])
            .range([0, 2 * Math.PI]);

        const radiusScale = d3.scaleLinear()
            .domain([0, 1])
            .range([0, radius]);

        // Draw grid circles
        const gridLevels = 5;
        for (let i = 1; i <= gridLevels; i++) {
            g.append('circle')
                .attr('r', radius * i / gridLevels)
                .attr('fill', 'none')
                .attr('stroke', '#e2e8f0')
                .attr('stroke-width', 1);
        }

        // Draw axes
        factors.forEach((factor, i) => {
            const angle = angleScale(i) - Math.PI / 2;
            const x = Math.cos(angle) * radius;
            const y = Math.sin(angle) * radius;

            g.append('line')
                .attr('x1', 0)
                .attr('y1', 0)
                .attr('x2', x)
                .attr('y2', y)
                .attr('stroke', '#cbd5e0')
                .attr('stroke-width', 1);

            // Add labels
            g.append('text')
                .attr('x', x * 1.1)
                .attr('y', y * 1.1)
                .attr('text-anchor', 'middle')
                .attr('dominant-baseline', 'middle')
                .style('font-size', '12px')
                .text(factor.name);
        });

        // Add data polygon (will be updated in updateVisualization)
        g.append('path')
            .attr('id', `${chartId}-polygon`)
            .attr('fill', this.colors.primary)
            .attr('fill-opacity', 0.3)
            .attr('stroke', this.colors.primary)
            .attr('stroke-width', 2);
    }

    createPathwaysChart(container, pathways) {
        // Simplified pathways visualization
        const width = this.dimensions.width;
        const height = 400;

        const svg = container.append('svg')
            .attr('width', width)
            .attr('height', height);

        const barHeight = height / pathways.length - 20;
        const xScale = d3.scaleLinear()
            .domain([0, 1])
            .range([100, width - 100]);

        pathways.forEach((pathway, i) => {
            const y = i * (barHeight + 20) + 20;
            
            // Pathway bar
            svg.append('rect')
                .attr('x', 100)
                .attr('y', y)
                .attr('width', xScale(pathway.probability) - 100)
                .attr('height', barHeight)
                .attr('fill', this.colors.info)
                .attr('opacity', 0.7);

            // Pathway label
            svg.append('text')
                .attr('x', 10)
                .attr('y', y + barHeight / 2)
                .attr('dominant-baseline', 'middle')
                .style('font-size', '14px')
                .text(pathway.name);

            // Probability text
            svg.append('text')
                .attr('x', xScale(pathway.probability) + 10)
                .attr('y', y + barHeight / 2)
                .attr('dominant-baseline', 'middle')
                .style('font-size', '12px')
                .text(`${(pathway.probability * 100).toFixed(0)}%`);
        });
    }

    createBubbleChart(container, modes) {
        const width = this.dimensions.width;
        const height = 400;

        const svg = container.append('svg')
            .attr('width', width)
            .attr('height', height);

        const radiusScale = d3.scaleSqrt()
            .domain([0, d3.max(modes, d => d.frequency)])
            .range([10, 50]);

        const colorScale = d3.scaleOrdinal()
            .domain(['low', 'medium', 'high'])
            .range([this.colors.success, this.colors.warning, this.colors.danger]);

        const simulation = d3.forceSimulation(modes)
            .force('charge', d3.forceManyBody().strength(50))
            .force('center', d3.forceCenter(width / 2, height / 2))
            .force('collision', d3.forceCollide().radius(d => radiusScale(d.frequency) + 2));

        const bubbles = svg.selectAll('.bubble')
            .data(modes)
            .enter()
            .append('g')
            .attr('class', 'bubble');

        bubbles.append('circle')
            .attr('r', d => radiusScale(d.frequency))
            .attr('fill', d => colorScale(d.severity))
            .attr('opacity', 0.7);

        bubbles.append('text')
            .attr('text-anchor', 'middle')
            .attr('dominant-baseline', 'middle')
            .style('font-size', '10px')
            .style('fill', 'white')
            .text(d => d.name.split(' ')[0]);

        simulation.on('tick', () => {
            bubbles.attr('transform', d => `translate(${d.x}, ${d.y})`);
        });
    }

    createImpactChart(container, dimensions) {
        const width = this.dimensions.width;
        const height = 400;
        const margin = this.dimensions.margin;

        const svg = container.append('svg')
            .attr('width', width)
            .attr('height', height);

        const g = svg.append('g')
            .attr('transform', `translate(${margin.left}, ${margin.top})`);

        const xScale = d3.scaleLinear()
            .domain([0, 1])
            .range([0, width - margin.left - margin.right]);

        const yScale = d3.scaleLinear()
            .domain([0, 1])
            .range([height - margin.top - margin.bottom, 0]);

        // Add axes
        g.append('g')
            .attr('transform', `translate(0, ${height - margin.top - margin.bottom})`)
            .call(d3.axisBottom(xScale));

        g.append('g')
            .call(d3.axisLeft(yScale));

        // Add axis labels
        g.append('text')
            .attr('x', (width - margin.left - margin.right) / 2)
            .attr('y', height - margin.top - margin.bottom + 40)
            .attr('text-anchor', 'middle')
            .text('Centralization Level');

        g.append('text')
            .attr('transform', 'rotate(-90)')
            .attr('x', -(height - margin.top - margin.bottom) / 2)
            .attr('y', -40)
            .attr('text-anchor', 'middle')
            .text('Impact Level');

        // Draw impact curves
        const line = d3.line()
            .x(d => xScale(d.x))
            .y(d => yScale(d.y))
            .curve(d3.curveMonotoneX);

        dimensions.forEach((dimension, i) => {
            const data = this.generateImpactCurve(dimension);
            
            g.append('path')
                .datum(data)
                .attr('fill', 'none')
                .attr('stroke', d3.schemeCategory10[i])
                .attr('stroke-width', 2)
                .attr('d', line);

            // Add legend
            g.append('text')
                .attr('x', width - margin.left - margin.right - 150)
                .attr('y', 20 + i * 20)
                .style('fill', d3.schemeCategory10[i])
                .text(dimension.name);
        });
    }

    generateImpactCurve(dimension) {
        const points = [];
        for (let x = 0; x <= 1; x += 0.01) {
            let y;
            switch (dimension.impactFunction) {
                case 'exponential_decay':
                    y = dimension.baseline * Math.exp(-dimension.parameters.decay_rate * x);
                    break;
                case 'linear_decay':
                    y = Math.max(0, dimension.baseline + dimension.parameters.slope * x);
                    break;
                case 'threshold_decay':
                    y = x < dimension.parameters.threshold ? 
                        dimension.baseline : 
                        dimension.baseline * Math.exp(-dimension.parameters.decay_rate * (x - dimension.parameters.threshold));
                    break;
                case 'sigmoid_decay':
                    y = dimension.baseline / (1 + Math.exp(dimension.parameters.steepness * (x - dimension.parameters.midpoint)));
                    break;
                default:
                    y = dimension.baseline;
            }
            points.push({ x, y });
        }
        return points;
    }

    updateVisualization() {
        // Update radar chart based on current parameters
        this.updateRadarChart(this.currentParameters);

        // Update analysis panel
        this.updateAnalysisPanel();
    }

    loadScenarioParameters() {
        const scenario = this.data.scenarios.find(s => s.name === this.currentScenario);
        if (scenario) {
            this.currentParameters = { ...scenario.parameters };

            // Update slider values
            Object.keys(scenario.parameters).forEach(param => {
                const slider = document.getElementById(`slider-${param}`);
                if (slider) {
                    slider.value = scenario.parameters[param];
                    this.updateParameterDisplay(param, scenario.parameters[param]);
                }
            });
        }
    }

    updateParameterDisplay(parameter, value) {
        const displayElement = document.getElementById(`${parameter}-value`);
        if (displayElement) {
            displayElement.textContent = `${Math.round(value * 100)}%`;
        }
    }

    updateAnalysisPanel() {
        // Calculate performance score
        const performanceScore = this.dataProcessor.calculatePerformanceScore(this.currentParameters);
        const scoreElement = document.getElementById('performance-score');
        if (scoreElement) {
            const scorePercent = Math.round(performanceScore * 100);
            scoreElement.innerHTML = `
                <div class="score-bar">
                    <div class="score-fill" style="width: ${scorePercent}%; background-color: ${this.getScoreColor(performanceScore)}"></div>
                </div>
                <span class="score-text">${scorePercent}%</span>
            `;
        }

        // Calculate most likely pathway
        const pathwayResult = this.dataProcessor.getMostLikelyPathway(this.currentParameters);
        const pathwayElement = document.getElementById('likely-pathway');
        if (pathwayElement && pathwayResult.pathway) {
            pathwayElement.innerHTML = `
                <strong>${pathwayResult.pathway.name}</strong><br>
                <small>Probability: ${Math.round(pathwayResult.probability * 100)}%</small><br>
                <small>${pathwayResult.pathway.characteristics[0]}</small>
            `;
        }

        // Calculate failure risk
        const riskResult = this.dataProcessor.calculateFailureRisk(this.currentParameters);
        const riskElement = document.getElementById('failure-risk');
        if (riskElement) {
            const riskPercent = Math.round(riskResult.totalRisk * 100);
            const topRisk = riskResult.riskBreakdown[0];
            riskElement.innerHTML = `
                <div class="risk-bar">
                    <div class="risk-fill" style="width: ${riskPercent}%; background-color: ${this.getRiskColor(riskResult.totalRisk)}"></div>
                </div>
                <span class="risk-text">${riskPercent}% overall risk</span><br>
                <small>Top risk: ${topRisk.name}</small>
            `;
        }

        // Generate recommendations
        const recommendations = this.dataProcessor.generateRecommendations(this.currentParameters);
        const recommendationsElement = document.getElementById('recommendations');
        if (recommendationsElement) {
            if (recommendations.length > 0) {
                const recHtml = recommendations.map(rec => `
                    <div class="recommendation ${rec.priority}">
                        <strong>${rec.type.toUpperCase()}:</strong> ${rec.message}
                    </div>
                `).join('');
                recommendationsElement.innerHTML = recHtml;
            } else {
                recommendationsElement.innerHTML = '<div class="recommendation success">Current configuration looks good! No immediate concerns identified.</div>';
            }
        }
    }

    getScoreColor(score) {
        if (score >= 0.8) return this.colors.success;
        if (score >= 0.6) return this.colors.info;
        if (score >= 0.4) return this.colors.warning;
        return this.colors.danger;
    }

    getRiskColor(risk) {
        if (risk >= 0.8) return this.colors.danger;
        if (risk >= 0.6) return this.colors.warning;
        if (risk >= 0.4) return this.colors.info;
        return this.colors.success;
    }

    updateRadarChart(parameters) {
        const factors = this.data.researchModels.performanceModel.factors;
        const radius = Math.min(this.dimensions.width, this.dimensions.height) / 2 - 100;
        
        const angleScale = d3.scaleLinear()
            .domain([0, factors.length])
            .range([0, 2 * Math.PI]);

        const radiusScale = d3.scaleLinear()
            .domain([0, 1])
            .range([0, radius]);

        // Generate path data
        const pathData = factors.map((factor, i) => {
            const angle = angleScale(i) - Math.PI / 2;
            const paramKey = this.getParameterKey(factor.name);
            const value = parameters[paramKey] || 0.5;
            const r = radiusScale(value);
            return [Math.cos(angle) * r, Math.sin(angle) * r];
        });

        // Close the path
        pathData.push(pathData[0]);

        const pathString = `M${pathData.map(d => d.join(',')).join('L')}Z`;

        d3.select('#performance-polygon')
            .transition()
            .duration(500)
            .attr('d', pathString);
    }

    getParameterKey(factorName) {
        const mapping = {
            'Information Openness': 'informationOpenness',
            'Community Autonomy': 'communityAutonomy',
            'Resource Accessibility': 'resourceAccessibility',
            'Governance Transparency': 'governanceTransparency',
            'External Support': 'externalSupport',
            'Centralization Pressure': 'centralizationPressure'
        };
        return mapping[factorName] || 'informationOpenness';
    }

    formatParameterName(paramKey) {
        return paramKey.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    }
}
